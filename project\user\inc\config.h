/*********************************************************************************************************************
* MSPM0G3507 Opensource Library 即（MSPM0G3507 开源库）是一个基于官方 SDK 接口的第三方开源库
* Copyright (c) 2022 SEEKFREE 逐飞科技
* 
* 本文件是 MSPM0G3507 开源库的一部分
* 
* MSPM0G3507 开源库 是免费软件
* 您可以根据自由软件基金会发布的 GPL（GNU General Public License，即 GNU通用公共许可证）的条款
* 即 GPL 的第3版（即 GPL3.0）或（您选择的）任何后来的版本，重新发布和/或修改它
* 
* 本开源库的发布是希望它能发挥作用，但并未对其作任何的保证
* 甚至没有隐含的适销性或适合特定用途的保证
* 更多细节请参见 GPL
* 
* 您应该在收到本开源库的同时收到一份 GPL 的副本
* 如果没有，请参阅<https://www.gnu.org/licenses/>
* 
* 额外注明：
* 本开源库使用 GPL3.0 开源许可证协议 以上许可申明为译文版本
* 许可申明英文版在 libraries/doc 文件夹下的 GPL3_permission_statement.txt 文件中
* 许可证副本在 libraries 文件夹下 即该文件夹下的 LICENSE 文件
* 欢迎各位使用并传播本程序 但修改内容时必须保留逐飞科技的版权声明（即本声明）
* 
* 文件名称          config
* 公司名称          成都逐飞科技有限公司
* 版本信息          查看 libraries/doc 文件夹内 version 文件 版本说明
* 开发环境          MDK 5.37
* 适用平台          MSPM0G3507
* 店铺链接          https://seekfree.taobao.com/
* 
* 修改记录
* 日期              作者                备注
* 2025-01-01       SeekFree            寻迹小车系统配置文件
********************************************************************************************************************/

#ifndef _CONFIG_H_
#define _CONFIG_H_

#include "zf_common_headfile.h"

//====================================================硬件引脚配置====================================================

//---------------------------------------------------电机驱动引脚配置---------------------------------------------------
// TB6612电机驱动模块引脚定义
// 左电机控制引脚
#define MOTOR_LEFT_PWM_PIN          PWM_TIM_A0_CH0_A0       // 左电机PWM控制引脚 (A0)
#define MOTOR_LEFT_DIR_PIN1         A7                      // 左电机方向控制引脚1 (IN1)
#define MOTOR_LEFT_DIR_PIN2         A8                      // 左电机方向控制引脚2 (IN2)

// 右电机控制引脚
#define MOTOR_RIGHT_PWM_PIN         PWM_TIM_A0_CH1_A1       // 右电机PWM控制引脚 (A1)
#define MOTOR_RIGHT_DIR_PIN1        A10                     // 右电机方向控制引脚1 (IN3)
#define MOTOR_RIGHT_DIR_PIN2        A11                     // 右电机方向控制引脚2 (IN4)

// TB6612使能引脚 (可选，如果硬件上直接接VCC可不使用)
#define MOTOR_ENABLE_PIN            A12                     // 电机驱动使能引脚 (STBY)

//---------------------------------------------------灰度传感器引脚配置---------------------------------------------------
// 感为8路灰度传感器引脚定义
#define SENSOR_ADC_PIN              ADC0_CH0_A27            // 传感器ADC数据引脚
#define SENSOR_ADDR_PIN0            A13                     // 传感器地址线0
#define SENSOR_ADDR_PIN1            A14                     // 传感器地址线1
#define SENSOR_ADDR_PIN2            A15                     // 传感器地址线2

//---------------------------------------------------调试接口引脚配置---------------------------------------------------
// 调试LED指示灯
#define DEBUG_LED_PIN               B0                      // 调试LED引脚
#define STATUS_LED_PIN              B1                      // 状态指示LED引脚

// 按键输入
#define KEY_START_PIN               B2                      // 启动按键引脚
#define KEY_CALIBRATE_PIN           B3                      // 校准按键引脚

//====================================================系统参数配置====================================================

//---------------------------------------------------电机控制参数---------------------------------------------------
// PWM频率设置
#define MOTOR_PWM_FREQUENCY         17000                   // 电机PWM频率 (Hz)

// 电机速度参数
#define MOTOR_BASE_SPEED            3000                    // 基础运行速度 (0-10000)
#define MOTOR_MAX_SPEED             8000                    // 最大运行速度 (0-10000)
#define MOTOR_MIN_SPEED             1000                    // 最小运行速度 (0-10000)
#define MOTOR_TURN_SPEED_LIMIT      6000                    // 转弯时最大速度限制

// 电机方向定义
#define MOTOR_FORWARD               1                       // 电机正转
#define MOTOR_BACKWARD              0                       // 电机反转

//---------------------------------------------------PID控制参数---------------------------------------------------
// PID控制周期
#define PID_CONTROL_PERIOD          5                       // PID控制周期 (ms)
#define PID_TIMER_INDEX             PIT_TIM_G6              // PID控制定时器

// PID参数初值 (可通过调试接口在线调整)
#define PID_KP_DEFAULT              2.5f                    // 比例系数初值
#define PID_KI_DEFAULT              0.1f                    // 积分系数初值  
#define PID_KD_DEFAULT              0.8f                    // 微分系数初值

// PID输出限制
#define PID_OUTPUT_LIMIT            5000                    // PID输出限幅值
#define PID_INTEGRAL_LIMIT          2000                    // 积分限幅值

//---------------------------------------------------传感器配置参数---------------------------------------------------
// 传感器基本参数
#define SENSOR_CHANNEL_NUM          8                       // 传感器通道数量
#define SENSOR_ADC_RESOLUTION       GANWEI_GRAYSCALE_ADC_12BITS  // ADC分辨率
#define SENSOR_EDITION              GANWEI_GRAYSCALE_CLASS_EDITION  // 传感器版本

// 传感器数据处理参数
#define SENSOR_FILTER_DEPTH         3                       // 数据滤波深度
#define SENSOR_THRESHOLD            2000                    // 黑白判断阈值
#define SENSOR_LINE_LOST_THRESHOLD  100                     // 丢线判断阈值

// 传感器位置权重 (用于计算黑线位置)
#define SENSOR_WEIGHT_0             -3.5f                   // 传感器0权重
#define SENSOR_WEIGHT_1             -2.5f                   // 传感器1权重
#define SENSOR_WEIGHT_2             -1.5f                   // 传感器2权重
#define SENSOR_WEIGHT_3             -0.5f                   // 传感器3权重
#define SENSOR_WEIGHT_4             0.5f                    // 传感器4权重
#define SENSOR_WEIGHT_5             1.5f                    // 传感器5权重
#define SENSOR_WEIGHT_6             2.5f                    // 传感器6权重
#define SENSOR_WEIGHT_7             3.5f                    // 传感器7权重

//---------------------------------------------------系统状态参数---------------------------------------------------
// 系统运行模式
#define SYSTEM_MODE_STOP            0                       // 停止模式
#define SYSTEM_MODE_CALIBRATE       1                       // 校准模式
#define SYSTEM_MODE_RUNNING         2                       // 运行模式
#define SYSTEM_MODE_DEBUG           3                       // 调试模式

// 系统安全参数
#define SYSTEM_WATCHDOG_TIMEOUT     100                     // 看门狗超时时间 (ms)
#define SYSTEM_LINE_LOST_TIMEOUT    500                     // 丢线超时时间 (ms)
#define SYSTEM_EMERGENCY_STOP_TIME  50                      // 紧急停车时间 (ms)

//---------------------------------------------------调试配置参数---------------------------------------------------
// 串口调试参数
#define DEBUG_UART_ENABLE           1                       // 使能串口调试
#define DEBUG_PRINT_PERIOD          100                     // 调试信息打印周期 (ms)
#define DEBUG_SENSOR_DATA           1                       // 打印传感器数据
#define DEBUG_PID_DATA              1                       // 打印PID数据
#define DEBUG_MOTOR_DATA            1                       // 打印电机数据

// 参数调试范围限制
#define PID_KP_MIN                  0.0f                    // Kp最小值
#define PID_KP_MAX                  10.0f                   // Kp最大值
#define PID_KI_MIN                  0.0f                    // Ki最小值
#define PID_KI_MAX                  5.0f                    // Ki最大值
#define PID_KD_MIN                  0.0f                    // Kd最小值
#define PID_KD_MAX                  5.0f                    // Kd最大值

//====================================================功能开关配置====================================================
// 功能模块使能开关
#define ENABLE_MOTOR_CONTROL        1                       // 使能电机控制
#define ENABLE_SENSOR_PROCESS       1                       // 使能传感器处理
#define ENABLE_PID_CONTROL          1                       // 使能PID控制
#define ENABLE_DEBUG_OUTPUT         1                       // 使能调试输出
#define ENABLE_LED_INDICATOR        1                       // 使能LED指示
#define ENABLE_KEY_INPUT            1                       // 使能按键输入

// 高级功能开关
#define ENABLE_AUTO_CALIBRATION     1                       // 使能自动校准
#define ENABLE_SPEED_ADAPTATION     1                       // 使能速度自适应
#define ENABLE_CURVE_DETECTION      0                       // 使能弯道检测 (暂未实现)
#define ENABLE_OBSTACLE_AVOIDANCE   0                       // 使能避障功能 (暂未实现)

//====================================================版本信息====================================================
#define SYSTEM_VERSION_MAJOR        1                       // 主版本号
#define SYSTEM_VERSION_MINOR        0                       // 次版本号
#define SYSTEM_VERSION_PATCH        0                       // 修订版本号
#define SYSTEM_BUILD_DATE           "2025-01-01"            // 编译日期

#endif // _CONFIG_H_
